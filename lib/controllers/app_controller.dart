import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../core/services/apple_payment_service.dart';
import '../core/services/network_service.dart';
import '../services/api_service.dart';
import '../shared/models/template_response_model.dart';

/// 应用全局数据控制器
/// 专门负责全局数据的加载、存储和管理，不包含业务逻辑
class AppController extends GetxController {
  static AppController get to => Get.find();

  final Logger _logger = Logger();
  late final ApiService _apiService;
  late final ApplePaymentService _paymentService;
  late final NetworkService _networkService;

  // 全局数据状态
  final Rx<TemplateData?> _templateData = Rx<TemplateData?>(null);
  final Rx<List<ProductInfo>?> _productInfo = Rx<List<ProductInfo>?>(null);
  final Rx<MembershipPeriod?> _membershipPeriod = Rx<MembershipPeriod?>(null);
  final _isInitialized = false.obs;
  final _isLoadingData = false.obs;
  final _networkRetryCount = 0.obs;
  final _maxRetryCount = 3;

  // Getters - 暴露响应式数据供其他 Controller 监听
  Rx<TemplateData?> get templateData => _templateData;
  Rx<List<ProductInfo>?> get productInfo => _productInfo;
  Rx<MembershipPeriod?> get membershipPeriod => _membershipPeriod;
  RxBool get isInitialized => _isInitialized;
  RxBool get isLoadingData => _isLoadingData;

  @override
  void onInit() {
    super.onInit();
    _logger.d('AppController 初始化开始 - 统一管理所有全局状态');

    // 获取依赖服务
    _apiService = Get.find<ApiService>();
    _paymentService = Get.find<ApplePaymentService>();
    _networkService = Get.find<NetworkService>();

    // 监听数据加载状态变化（调试用）
    ever(_isLoadingData, (bool loading) {
      _logger.d('AppController 数据加载状态变化：$loading');
    });

    // 设置网络状态监听
    _setupNetworkListener();

    // 初始化所有数据
    _initializeAllData();
  }

  /// 初始化所有应用数据
  Future<void> _initializeAllData() async {
    if (_isLoadingData.value) return;

    try {
      _isLoadingData.value = true;
      _logger.d('开始加载所有应用数据');

      // 优先加载模板数据，这是首页必需的
      await _loadTemplateData().catchError((e) => _logger.e('模板数据加载失败: $e'));

      // 模板数据加载完成后，立即设置loading为false，让UI可以显示
      _isLoadingData.value = false;
      _logger.d('核心模板数据加载完成，UI可以显示');

      // 并行加载其他数据，这些不影响首页显示
      Future.wait([
        _loadProductInfo().catchError((e) => _logger.e('产品信息加载失败: $e')),
        _loadMembershipPeriod().catchError((e) => _logger.e('会员状态加载失败: $e')),
      ]).then((_) {
        _isInitialized.value = true;
        _logger.d('所有应用数据加载完成，isInitialized: ${_isInitialized.value}');
      });
    } catch (e) {
      _logger.e('应用数据加载过程中出现未预期错误: $e');
      _isLoadingData.value = false;
    }
  }

  /// 加载模板数据
  Future<void> _loadTemplateData() async {
    try {
      _logger.d('开始加载模板数据');

      // 检查网络连接
      if (!_networkService.hasConnection()) {
        _logger.w('无网络连接，等待网络恢复后重试');
        _scheduleNetworkRetry();
        return;
      }

      final templateData = await _apiService.getTemplates();
      _templateData.value = templateData;
      _networkRetryCount.value = 0; // 重置重试计数
      _logger.d('模板数据加载成功');
    } catch (e) {
      _logger.e('模板数据加载失败: $e');

      // 如果是网络相关错误且未达到最大重试次数，则安排重试
      if (_shouldRetryNetworkRequest(e) &&
          _networkRetryCount.value < _maxRetryCount) {
        _networkRetryCount.value++;
        _logger.d('安排第${_networkRetryCount.value}次重试');
        _scheduleNetworkRetry();
      } else {
        _logger.e('模板数据加载最终失败，停止重试');
        // 模板数据加载失败不阻止应用启动，但提供手动重试选项
        _showRetryOption();
      }
    }
  }

  /// 加载产品信息
  Future<void> _loadProductInfo() async {
    try {
      _logger.d('开始加载产品信息');
      final productInfoList = await _apiService.getProductInfo();
      _productInfo.value = productInfoList;
      _logger.d('产品信息加载成功，共 ${productInfoList.length} 个产品');
    } catch (e) {
      _logger.e('产品信息加载失败: $e');
      // 产品信息加载失败不阻止应用启动
    }
  }

  /// 加载会员状态
  Future<void> _loadMembershipPeriod({bool isAfterPurchase = false}) async {
    try {
      _logger.d('开始加载会员状态 (购买后: $isAfterPurchase)');
      final membershipPeriod = await _paymentService.getMembershipPeriod(
        isAfterPurchase: isAfterPurchase,
      );
      _membershipPeriod.value = membershipPeriod;
      _logger.d('会员状态加载成功: ${membershipPeriod?.toString() ?? "无会员"}');
    } catch (e) {
      _logger.e('会员状态加载失败: $e');
      // 会员状态加载失败不阻止应用启动
    }
  }

  /// 刷新会员状态
  Future<void> refreshMembershipPeriod({bool isAfterPurchase = false}) async {
    await _loadMembershipPeriod(isAfterPurchase: isAfterPurchase);
  }

  /// 刷新所有数据
  Future<void> refreshAllData() async {
    _isInitialized.value = false;
    await _initializeAllData();
  }

  /// 判断是否应该重试网络请求
  bool _shouldRetryNetworkRequest(dynamic error) {
    final errorMessage = error.toString().toLowerCase();
    return errorMessage.contains('timeout') ||
        errorMessage.contains('connection') ||
        errorMessage.contains('network') ||
        errorMessage.contains('socket');
  }

  /// 安排网络重试
  void _scheduleNetworkRetry() {
    // 延迟重试，给用户时间处理权限弹窗
    final retryDelay = Duration(seconds: 2 + _networkRetryCount.value * 2);
    _logger.d('将在${retryDelay.inSeconds}秒后重试网络请求');

    Future.delayed(retryDelay, () async {
      if (_templateData.value == null) {
        _logger.d('执行网络重试');
        await _loadTemplateData();
      }
    });
  }

  /// 显示重试选项
  void _showRetryOption() {
    // 延迟显示，避免在应用启动时立即弹出
    Future.delayed(const Duration(seconds: 1), () {
      if (_templateData.value == null && Get.context != null) {
        Get.snackbar(
          '网络连接失败',
          '无法加载数据，请检查网络连接后点击重试',
          snackPosition: SnackPosition.TOP,
          duration: const Duration(seconds: 5),
          mainButton: TextButton(
            onPressed: () {
              Get.back(); // 关闭snackbar
              _networkRetryCount.value = 0; // 重置重试计数
              _loadTemplateData(); // 手动重试
            },
            child: const Text('重试', style: TextStyle(color: Colors.white)),
          ),
        );
      }
    });
  }
}

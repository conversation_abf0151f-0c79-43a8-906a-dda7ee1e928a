import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

/// 网络状态监听服务
/// 监听网络连接状态变化，并提供网络状态查询功能
class NetworkService extends GetxService {
  static NetworkService get to => Get.find();

  final Logger _logger = Logger();
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;
  
  // 网络状态
  final _isConnected = true.obs;
  final _connectivityResults = <ConnectivityResult>[].obs;
  
  // 网络状态变化回调列表
  final List<Function(bool isConnected)> _networkChangeCallbacks = [];

  // Getters
  RxBool get isConnected => _isConnected;
  RxList<ConnectivityResult> get connectivityResults => _connectivityResults;

  @override
  void onInit() {
    super.onInit();
    _initializeNetworkMonitoring();
  }

  @override
  void onClose() {
    _connectivitySubscription.cancel();
    super.onClose();
  }

  /// 初始化网络监听
  void _initializeNetworkMonitoring() async {
    // 获取初始网络状态
    await _updateConnectivityStatus();

    // 监听网络状态变化
    _connectivitySubscription = Connectivity()
        .onConnectivityChanged
        .listen((List<ConnectivityResult> results) {
      _logger.d('网络状态变化: $results');
      _connectivityResults.value = results;
      _updateNetworkStatus(results);
    });
  }

  /// 更新网络连接状态
  Future<void> _updateConnectivityStatus() async {
    try {
      final results = await Connectivity().checkConnectivity();
      _connectivityResults.value = results;
      _updateNetworkStatus(results);
    } catch (e) {
      _logger.e('检查网络状态失败: $e');
      _isConnected.value = false;
    }
  }

  /// 更新网络状态并通知回调
  void _updateNetworkStatus(List<ConnectivityResult> results) {
    final wasConnected = _isConnected.value;
    final isNowConnected = !results.contains(ConnectivityResult.none);
    
    _isConnected.value = isNowConnected;

    if (!wasConnected && isNowConnected) {
      _logger.d('网络已连接，通知回调');
      // 网络从断开变为连接，通知所有回调
      for (final callback in _networkChangeCallbacks) {
        try {
          callback(true);
        } catch (e) {
          _logger.e('网络状态回调执行失败: $e');
        }
      }
    } else if (wasConnected && !isNowConnected) {
      _logger.d('网络已断开');
      // 网络从连接变为断开
      for (final callback in _networkChangeCallbacks) {
        try {
          callback(false);
        } catch (e) {
          _logger.e('网络状态回调执行失败: $e');
        }
      }
    }
  }

  /// 添加网络状态变化回调
  void addNetworkChangeCallback(Function(bool isConnected) callback) {
    _networkChangeCallbacks.add(callback);
  }

  /// 移除网络状态变化回调
  void removeNetworkChangeCallback(Function(bool isConnected) callback) {
    _networkChangeCallbacks.remove(callback);
  }

  /// 检查是否有网络连接
  bool hasConnection() {
    return _isConnected.value;
  }

  /// 检查是否有WiFi连接
  bool hasWifiConnection() {
    return _connectivityResults.contains(ConnectivityResult.wifi);
  }

  /// 检查是否有移动网络连接
  bool hasMobileConnection() {
    return _connectivityResults.contains(ConnectivityResult.mobile);
  }

  /// 获取网络类型描述
  String getNetworkTypeDescription() {
    if (_connectivityResults.contains(ConnectivityResult.wifi)) {
      return 'WiFi';
    } else if (_connectivityResults.contains(ConnectivityResult.mobile)) {
      return '移动网络';
    } else if (_connectivityResults.contains(ConnectivityResult.ethernet)) {
      return '以太网';
    } else if (_connectivityResults.contains(ConnectivityResult.vpn)) {
      return 'VPN';
    } else if (_connectivityResults.contains(ConnectivityResult.bluetooth)) {
      return '蓝牙';
    } else if (_connectivityResults.contains(ConnectivityResult.other)) {
      return '其他网络';
    } else {
      return '无网络连接';
    }
  }

  /// 手动刷新网络状态
  Future<void> refreshNetworkStatus() async {
    await _updateConnectivityStatus();
  }
}

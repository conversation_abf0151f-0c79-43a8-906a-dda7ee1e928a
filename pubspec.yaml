name: cartoon_camera
description: "卡通相机 - AI照片风格重绘应用"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # 基础图标
  cupertino_icons: ^1.0.8

  # 状态管理和路由
  get: ^4.6.6

  # 网络请求 (用于AI API调用)
  dio: ^5.4.0

  # UI相关
  flutter_screenutil: ^5.9.0
  cached_network_image: ^3.3.0
  flutter_cache_manager: ^3.3.1
  flutter_svg: ^2.0.9
  image_picker: ^1.0.4
  carousel_slider: ^5.1.1
  lottie: ^2.7.0

  # AI头像生成核心功能
  permission_handler: ^11.1.0
  flutter_oss_aliyun: ^6.4.2

  # 图片处理和编辑
  image: ^4.1.3

  # 文件和存储
  path_provider: ^2.1.1
  path: ^1.8.3

  # 分享功能
  share_plus: ^7.2.1

  # 保存到相册
  gallery_saver: ^2.3.2

  # 存储相关
  get_storage: ^2.1.1

  # 工具类
  logger: ^2.0.2+1
  connectivity_plus: ^5.0.2
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0

  # 苹果登录
  sign_in_with_apple: ^6.1.2
  crypto: ^3.0.3

  # 苹果内购支付
  in_app_purchase: ^3.1.13

  # JSON序列化
  json_annotation: ^4.9.0

  # 国际化
  intl: ^0.19.0

  # 友盟统计SDK
  umeng_common_sdk: ^1.2.9

  # WebView
  webview_flutter: ^4.4.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # 代码规范检查
  flutter_lints: ^5.0.0

  # 分析器
  analyzer: ^6.11.0

  # 代码生成
  build_runner: ^2.4.7
  json_serializable: ^6.7.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
